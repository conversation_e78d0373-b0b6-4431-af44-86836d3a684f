import { createTool } from "@mastra/core/tools";
import { z } from "zod";

/**
 * Công cụ gửi hình ảnh cho khách hàng
 * Sử dụng Supabase để gửi hình ảnh cho khách hàng
 */
export const sendImagesLinkTool = createTool({
  id: "send_images_link",
  description: "Dùng khi muốn gửi các hình ảnh cho khách hàng",
  inputSchema: z.object({
    images: z.array(z.string()).describe("Danh sách URL của các hình ảnh cần gửi cho khách hàng"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      console.log("Đang gửi hình ảnh cho khách hàng:", context);

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");
      // console.log(runtimeContext)

      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // G<PERSON>i hình ảnh cho khách hàng
      // Logic gửi hình ảnh sẽ được xử lý bởi onStepFinish trong agent.service.ts
      console.log('Đã chuẩn bị gửi: ', context.images);

      return {
        success: true,
        images: context.images, // Truyền images để onStepFinish có thể xử lý
        message: "Đã chuẩn bị gửi hình ảnh cho người dùng",
      };
    } catch (error: any) {
      console.error("Lỗi khi gửi hình ảnh:", error);
      return {
        success: false,
        error: `Lỗi khi gửi hình ảnh: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});